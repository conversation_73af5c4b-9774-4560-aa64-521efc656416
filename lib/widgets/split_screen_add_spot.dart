import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:latlong2/latlong.dart';
import 'package:uuid/uuid.dart';
import '../models/fishing_spot.dart';

import '../services/service_locator.dart';
import '../services/image_upload_service.dart';
import '../services/presigned_upload_service.dart';
import 'upload_progress_dialog.dart';

/// 分屏添加钓点组件
class SplitScreenAddSpot extends StatefulWidget {
  /// 钓点位置
  final LatLng location;

  /// 位置更新回调
  final Function(LatLng) onLocationChanged;

  /// 关闭回调
  final VoidCallback onClose;

  /// 成功添加钓点回调
  final Function(FishingSpot) onSpotAdded;

  /// 建议的钓点名称
  final String? suggestedName;

  const SplitScreenAddSpot({
    super.key,
    required this.location,
    required this.onLocationChanged,
    required this.onClose,
    required this.onSpotAdded,
    this.suggestedName,
  });

  @override
  State<SplitScreenAddSpot> createState() => _SplitScreenAddSpotState();
}

class _SplitScreenAddSpotState extends State<SplitScreenAddSpot> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();

  String _selectedSpotType = '野钓';
  final List<String> _spotTypes = ['野钓', '黑坑'];

  String _selectedFishType = '淡水鱼';
  final List<String> _fishTypes = ['鲢鳙', '淡水鱼', '鲫鱼鲤鱼', '路亚鱼'];

  String _selectedWaterLevel = '正常';
  final List<String> _waterLevels = ['正常', '涨水', '退水'];

  String _selectedBait = '商品饵';
  final List<String> _baits = ['商品饵', '自制饵', '蚯蚓'];

  List<File> _selectedImages = [];
  bool _isLoading = false;

  // 图片上传服务
  final ImageUploadService _imageUploadService = ImageUploadService();
  final PresignedUploadService _presignedService = PresignedUploadService();

  @override
  void initState() {
    super.initState();
    // 如果有建议的钓点名称，设置到名称字段中
    if (widget.suggestedName != null &&
        widget.suggestedName!.trim().isNotEmpty) {
      _nameController.text = widget.suggestedName!;
    }
  }

  @override
  void didUpdateWidget(SplitScreenAddSpot oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 当建议名称更新时，如果输入框为空，则填充新的建议名称
    if (widget.suggestedName != oldWidget.suggestedName &&
        widget.suggestedName != null &&
        widget.suggestedName!.trim().isNotEmpty &&
        _nameController.text.trim().isEmpty) {
      _nameController.text = widget.suggestedName!;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.5,
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black26,
            blurRadius: 10,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 标题和关闭按钮
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          '添加钓点',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.close),
                          onPressed: widget.onClose,
                        ),
                      ],
                    ),

                    // 位置信息
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.blue.shade50,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.location_on, color: Colors.blue),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              '位置: ${widget.location.latitude.toStringAsFixed(6)}, ${widget.location.longitude.toStringAsFixed(6)}',
                              style: const TextStyle(fontSize: 14),
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 24),

                    Expanded(
                      child: SingleChildScrollView(
                        child: Padding(
                          padding: const EdgeInsets.only(top: 16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // 钓点名称
                              TextFormField(
                                controller: _nameController,
                                decoration: const InputDecoration(
                                  labelText: '钓点名称 *',
                                  border: OutlineInputBorder(),
                                ),
                                validator: (value) {
                                  if (value == null || value.trim().isEmpty) {
                                    return '请输入钓点名称';
                                  }
                                  return null;
                                },
                              ),

                              const SizedBox(height: 16),

                              // 四个选择框在同一行：钓点类型、鱼种、水位、饵料
                              Row(
                                children: [
                                  // 钓点类型
                                  Expanded(
                                    child: DropdownButtonFormField<String>(
                                      value: _selectedSpotType,
                                      decoration: const InputDecoration(
                                        labelText: '类型',
                                        border: OutlineInputBorder(),
                                        contentPadding: EdgeInsets.symmetric(
                                          horizontal: 8,
                                          vertical: 12,
                                        ),
                                      ),
                                      items:
                                          _spotTypes.map((type) {
                                            return DropdownMenuItem(
                                              value: type,
                                              child: Text(
                                                type,
                                                style: const TextStyle(
                                                  fontSize: 14,
                                                ),
                                              ),
                                            );
                                          }).toList(),
                                      onChanged: (value) {
                                        setState(() {
                                          _selectedSpotType = value!;
                                        });
                                      },
                                    ),
                                  ),

                                  const SizedBox(width: 4),

                                  // 鱼种选择
                                  Expanded(
                                    child: DropdownButtonFormField<String>(
                                      value: _selectedFishType,
                                      decoration: const InputDecoration(
                                        labelText: '鱼种',
                                        border: OutlineInputBorder(),
                                        contentPadding: EdgeInsets.symmetric(
                                          horizontal: 8,
                                          vertical: 12,
                                        ),
                                      ),
                                      items:
                                          _fishTypes.map((type) {
                                            return DropdownMenuItem(
                                              value: type,
                                              child: Text(
                                                type,
                                                style: const TextStyle(
                                                  fontSize: 14,
                                                ),
                                              ),
                                            );
                                          }).toList(),
                                      onChanged: (value) {
                                        setState(() {
                                          _selectedFishType = value!;
                                        });
                                      },
                                    ),
                                  ),

                                  const SizedBox(width: 4),

                                  // 水位选择
                                  Expanded(
                                    child: DropdownButtonFormField<String>(
                                      value: _selectedWaterLevel,
                                      decoration: const InputDecoration(
                                        labelText: '水位',
                                        border: OutlineInputBorder(),
                                        contentPadding: EdgeInsets.symmetric(
                                          horizontal: 8,
                                          vertical: 12,
                                        ),
                                      ),
                                      items:
                                          _waterLevels.map((level) {
                                            return DropdownMenuItem(
                                              value: level,
                                              child: Text(
                                                level,
                                                style: const TextStyle(
                                                  fontSize: 14,
                                                ),
                                              ),
                                            );
                                          }).toList(),
                                      onChanged: (value) {
                                        setState(() {
                                          _selectedWaterLevel = value!;
                                        });
                                      },
                                    ),
                                  ),

                                  const SizedBox(width: 4),

                                  // 饵料选择
                                  Expanded(
                                    child: DropdownButtonFormField<String>(
                                      value: _selectedBait,
                                      decoration: const InputDecoration(
                                        labelText: '饵料',
                                        border: OutlineInputBorder(),
                                        contentPadding: EdgeInsets.symmetric(
                                          horizontal: 8,
                                          vertical: 12,
                                        ),
                                      ),
                                      items:
                                          _baits.map((bait) {
                                            return DropdownMenuItem(
                                              value: bait,
                                              child: Text(
                                                bait,
                                                style: const TextStyle(
                                                  fontSize: 14,
                                                ),
                                              ),
                                            );
                                          }).toList(),
                                      onChanged: (value) {
                                        setState(() {
                                          _selectedBait = value!;
                                        });
                                      },
                                    ),
                                  ),
                                ],
                              ),

                              const SizedBox(height: 16),

                              // 照片上传
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(
                                    '钓点照片:',
                                    style: TextStyle(fontSize: 16),
                                  ),
                                  const SizedBox(height: 8),
                                  SizedBox(
                                    height: 80,
                                    child: ListView.builder(
                                      scrollDirection: Axis.horizontal,
                                      itemCount:
                                          _selectedImages.length +
                                          1, // +1 for the add button
                                      itemBuilder: (context, index) {
                                        if (index == _selectedImages.length) {
                                          // 添加照片的方框
                                          return Container(
                                            width: 80,
                                            height: 80,
                                            margin: const EdgeInsets.only(
                                              right: 8,
                                            ),
                                            decoration: BoxDecoration(
                                              border: Border.all(
                                                color: Colors.grey.shade400,
                                                width: 2,
                                                style: BorderStyle.solid,
                                              ),
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                              color: Colors.grey.shade100,
                                            ),
                                            child: InkWell(
                                              onTap: _pickImage,
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                              child: const Icon(
                                                Icons.add,
                                                size: 32,
                                                color: Colors.grey,
                                              ),
                                            ),
                                          );
                                        } else {
                                          // 已选择的照片缩略图
                                          return Container(
                                            width: 80,
                                            height: 80,
                                            margin: const EdgeInsets.only(
                                              right: 8,
                                            ),
                                            child: Stack(
                                              children: [
                                                ClipRRect(
                                                  borderRadius:
                                                      BorderRadius.circular(8),
                                                  child: Image.file(
                                                    _selectedImages[index],
                                                    width: 80,
                                                    height: 80,
                                                    fit: BoxFit.cover,
                                                  ),
                                                ),
                                                Positioned(
                                                  top: 4,
                                                  right: 4,
                                                  child: GestureDetector(
                                                    onTap:
                                                        () =>
                                                            _removeImage(index),
                                                    child: Container(
                                                      width: 20,
                                                      height: 20,
                                                      decoration:
                                                          const BoxDecoration(
                                                            color: Colors.red,
                                                            shape:
                                                                BoxShape.circle,
                                                          ),
                                                      child: const Icon(
                                                        Icons.close,
                                                        color: Colors.white,
                                                        size: 14,
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          );
                                        }
                                      },
                                    ),
                                  ),
                                ],
                              ),

                              const SizedBox(height: 16),

                              // 钓点描述
                              TextFormField(
                                controller: _descriptionController,
                                decoration: const InputDecoration(
                                  labelText: '钓点描述',
                                  border: OutlineInputBorder(),
                                ),
                                maxLines: 3,
                              ),

                              const SizedBox(height: 24),

                              // 发布按钮
                              SizedBox(
                                width: double.infinity,
                                child: ElevatedButton(
                                  onPressed: _isLoading ? null : _handleSubmit,
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor:
                                        Theme.of(context).primaryColor,
                                    foregroundColor: Colors.white,
                                    padding: const EdgeInsets.symmetric(
                                      vertical: 16,
                                    ),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                  ),
                                  child: const Text(
                                    '发布钓点',
                                    style: TextStyle(fontSize: 16),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
    );
  }

  // 选择图片 - 支持批量选择
  Future<void> _pickImage() async {
    // 显示选择对话框：相机、单选图库或批量选择
    final String? choice = await showDialog<String>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('选择图片来源'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.camera_alt),
                title: const Text('拍照'),
                onTap: () => Navigator.of(context).pop('camera'),
              ),
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text('从图库选择单张'),
                onTap: () => Navigator.of(context).pop('gallery_single'),
              ),
              ListTile(
                leading: const Icon(Icons.photo_library_outlined),
                title: const Text('从图库批量选择'),
                subtitle: const Text('一次选择多张图片'),
                onTap: () => Navigator.of(context).pop('gallery_multiple'),
              ),
            ],
          ),
        );
      },
    );

    if (choice != null) {
      final ImagePicker picker = ImagePicker();

      switch (choice) {
        case 'camera':
          final XFile? image = await picker.pickImage(
            source: ImageSource.camera,
          );
          if (image != null) {
            setState(() {
              _selectedImages.add(File(image.path));
            });
          }
          break;

        case 'gallery_single':
          final XFile? image = await picker.pickImage(
            source: ImageSource.gallery,
          );
          if (image != null) {
            setState(() {
              _selectedImages.add(File(image.path));
            });
          }
          break;

        case 'gallery_multiple':
          await _pickMultipleImages();
          break;
      }
    }
  }

  // 批量选择图片
  Future<void> _pickMultipleImages() async {
    try {
      final ImagePicker picker = ImagePicker();
      final List<XFile> images = await picker.pickMultiImage(
        imageQuality: 80,
        maxWidth: 1920,
        maxHeight: 1080,
      );

      if (images.isNotEmpty) {
        setState(() {
          // 将选择的图片添加到现有列表中
          _selectedImages.addAll(images.map((xFile) => File(xFile.path)));
        });

        // 显示选择结果
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('已选择 ${images.length} 张图片'),
              duration: const Duration(seconds: 2),
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('批量选择图片失败: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('选择图片失败，请重试'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  // 带进度显示的图片上传
  Future<void> _uploadImagesWithProgress(FishingSpot spot, dynamic user) async {
    final progressController = StreamController<UploadProgress>();

    // 显示上传进度对话框
    if (mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder:
            (context) => UploadProgressDialog(
              totalImages: _selectedImages.length,
              progressStream: progressController.stream,
            ),
      );
    }

    try {
      // 开始上传
      final uploadResults = await _imageUploadService.uploadImagesWithProgress(
        imageFiles: _selectedImages,
        userId: user.id,
        spotId: spot.id,
        progressController: progressController,
      );

      // 将上传结果保存到PocketBase
      if (uploadResults.isNotEmpty) {
        final photoDataList = <PhotoRecordData>[];
        for (int i = 0; i < uploadResults.length; i++) {
          final result = uploadResults[i];
          photoDataList.add(
            PhotoRecordData(
              filename: result.path.split('/').last,
              url: result.url,
              thumbnailUrl: result.thumbnailUrl,
              storagePath: result.path,
              thumbnailPath: result.thumbnailPath,
              sortOrder: i,
              fileSize: result.fileSize,
              mimeType: result.mimeType,
            ),
          );
        }

        // 批量保存照片记录
        final saveSuccess = await _presignedService.saveBatchPhotoRecords(
          spotId: spot.id,
          userId: user.id,
          photoData: photoDataList,
        );

        if (!saveSuccess) {
          debugPrint('照片记录保存失败');
        }
      }
    } catch (e) {
      debugPrint('图片上传失败: $e');
      // 图片上传失败不影响钓点发布，只显示警告
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('钓点发布成功，但图片上传失败: $e')));
      }
    } finally {
      // 关闭进度控制器
      await progressController.close();
    }
  }

  // 移除图片
  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }

  // 处理提交
  Future<void> _handleSubmit() async {
    if (!Services.auth.isLoggedIn) {
      // 跳转到登录页面，等待登录结果
      final result = await Navigator.pushNamed(context, '/login');
      // 如果登录成功，继续发布流程
      if (result == true && Services.auth.isLoggedIn) {
        // 登录成功后，递归调用自己继续发布
        await _handleSubmit();
      }
      return;
    }

    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final user = Services.auth.currentUser;

      // 创建钓点对象
      String description = _descriptionController.text.trim();

      // 将水位和饵料信息添加到描述中
      List<String> additionalInfo = [];
      if (_selectedWaterLevel != '正常') {
        additionalInfo.add('水位：$_selectedWaterLevel');
      }
      if (_selectedBait != '商品饵') {
        additionalInfo.add('饵料：$_selectedBait');
      }

      if (additionalInfo.isNotEmpty) {
        if (description.isNotEmpty) {
          description += '\n\n';
        }
        description += additionalInfo.join('，');
      }

      final spot = FishingSpot(
        id: const Uuid().v4(),
        name: _nameController.text.trim(),
        description: description,
        latitude: widget.location.latitude,
        longitude: widget.location.longitude,
        userId: user?.id ?? '',
        spotType: _selectedSpotType,
        fishTypes: _selectedFishType,
        created: DateTime.now(),
        updated: DateTime.now(),
      );

      // 添加钓点
      final addedSpot = await Services.fishingSpot.addSpot(spot);

      if (addedSpot != null) {
        // 如果有选择的图片，上传图片（带进度显示）
        if (_selectedImages.isNotEmpty) {
          await _uploadImagesWithProgress(addedSpot, user!);
        }

        // 添加成功，调用回调
        widget.onSpotAdded(addedSpot);

        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('钓点发布成功！')));
          widget.onClose();
        }
      } else {
        throw Exception('添加钓点失败');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('发布失败: $e')));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
